# Transformer模型详细注释总结

## 完成的注释改进

### ✅ 1. Tensor Shape详细标注

为所有tensor格式的变量和参数添加了详细的shape注释：

#### 位置编码 (PositionalEncoding)
```python
# 输入: [batch_size, seq_len, d_model] - token embeddings
# 位置编码: [1, max_seq_len, d_model] - 预计算的位置编码
# 输出: [batch_size, seq_len, d_model] - 带位置信息的embeddings
```

#### 多头注意力 (MultiHeadAttention)
```python
# Q, K, V: [batch_size, seq_len, d_model] -> [batch_size, n_heads, seq_len, d_k]
# 注意力分数: [batch_size, n_heads, seq_len_q, seq_len_k]
# 注意力权重: [batch_size, n_heads, seq_len_q, seq_len_k]
# 输出: [batch_size, n_heads, seq_len_q, d_k] -> [batch_size, seq_len_q, d_model]
```

#### 前馈网络 (<PERSON><PERSON><PERSON>or<PERSON>)
```python
# 输入: [batch_size, seq_len, d_model]
# 第一层: [batch_size, seq_len, d_ff] (扩展维度)
# 第二层: [batch_size, seq_len, d_model] (恢复维度)
```

### ✅ 2. 数据流转过程详解

为每个组件添加了详细的数据流转说明：

#### Transformer整体流程
```
1. 输入: src [batch_size, src_len], tgt [batch_size, tgt_len]
2. 创建掩码: encoder_mask, decoder_self_mask, decoder_cross_mask
3. 编码阶段: src -> encoder_output [batch_size, src_len, d_model]
4. 解码阶段: tgt + encoder_output -> decoder_output [batch_size, tgt_len, d_model]
5. 输出投影: decoder_output -> logits [batch_size, tgt_len, vocab_size_it]
```

#### 多头注意力流程
```
1. 输入: query, key, value [batch_size, seq_len, d_model]
2. 线性变换: Q, K, V [batch_size, seq_len, d_model]
3. 重塑为多头: [batch_size, n_heads, seq_len, d_k]
4. 计算注意力: scaled dot-product attention
5. 合并多头: [batch_size, seq_len, d_model]
6. 输出投影: [batch_size, seq_len, d_model]
```

### ✅ 3. Mask计算详细解释

重点解释了三种mask的计算过程和作用：

#### 编码器填充掩码 (encoder_mask)
```python
# 目的: 防止注意力机制关注到填充token (PAD)
# 计算: src == pad_id -> [batch_size, src_len] -> [batch_size, 1, 1, src_len]
# 示例: src = [[1, 2, 0, 0], [3, 4, 5, 0]] (0是PAD)
# 结果: encoder_mask = [[[False, False, True, True]], [[False, False, False, True]]]
```

#### 解码器自注意力掩码 (decoder_self_mask)
```python
# 包含两部分: 填充掩码 + 前瞻掩码

# 1. 填充掩码: 屏蔽PAD token
decoder_padding_mask = (tgt == pad_id).unsqueeze(1).unsqueeze(2)

# 2. 前瞻掩码: 防止解码器看到未来的token (因果性)
# torch.triu创建上三角矩阵，diagonal=1表示主对角线上方的元素为1
look_ahead_mask = torch.triu(torch.ones(tgt_len, tgt_len), diagonal=1).bool()

# 示例: 如果tgt_len=4，则look_ahead_mask为:
# [[False, True,  True,  True ],
#  [False, False, True,  True ],
#  [False, False, False, True ],
#  [False, False, False, False]]

# 3. 结合两种掩码
decoder_self_mask = decoder_padding_mask | look_ahead_mask
```

#### 解码器交叉注意力掩码 (decoder_cross_mask)
```python
# 目的: 防止解码器关注编码器输出中的PAD位置
# 与编码器掩码相同
decoder_cross_mask = encoder_mask
```

### ✅ 4. 缩放点积注意力详解

详细解释了注意力机制的核心计算：

```python
# 公式: Attention(Q,K,V) = softmax(QK^T/√d_k)V

# 步骤1: 计算注意力分数 QK^T/√d_k
scores = torch.matmul(q, k.transpose(-2, -1)) / math.sqrt(d_k)

# 步骤2: 应用掩码
scores = scores.masked_fill(mask == True, -1e9)

# 步骤3: 计算注意力权重 (softmax归一化)
attention_weights = torch.softmax(scores, dim=-1)

# 步骤4: 计算加权输出
output = torch.matmul(attention_weights, v)
```

## 学习要点

### 1. Tensor维度变化
- 理解每一步操作如何改变tensor的shape
- 注意batch维度的广播机制
- 掌握多头注意力中的维度重塑

### 2. 掩码机制
- **填充掩码**: 屏蔽PAD token，防止模型关注无意义的填充
- **前瞻掩码**: 确保解码器的因果性，不能看到未来token
- **交叉注意力掩码**: 解码器关注编码器时的掩码

### 3. 数据流转
- 编码器: token -> embedding -> 多层自注意力 -> 输出表示
- 解码器: token -> embedding -> 自注意力 -> 交叉注意力 -> 前馈网络
- 注意力: Q,K,V -> 分数计算 -> 权重归一化 -> 加权求和

### 4. 关键公式
- 位置编码: PE(pos,2i) = sin(pos/10000^(2i/d_model))
- 注意力: Attention(Q,K,V) = softmax(QK^T/√d_k)V
- 前馈网络: FFN(x) = max(0, xW₁ + b₁)W₂ + b₂

## 使用建议

1. **阅读顺序**: 建议按照数据流转的顺序阅读代码
2. **调试技巧**: 使用print语句打印tensor shape来验证理解
3. **实验建议**: 修改小参数运行quick模式来观察效果
4. **深入学习**: 结合论文"Attention Is All You Need"理解原理

现在transformer模型的每个组件都有了详细的注释，包括tensor shape、数据流转过程和mask计算，便于学习和理解！
